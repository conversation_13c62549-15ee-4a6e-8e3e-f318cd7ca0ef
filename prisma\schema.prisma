// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// User Management
model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          Role      @default(USER)
  status        UserStatus @default(ACTIVE)
  departmentId  String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts      Account[]
  sessions      Session[]
  department    Department? @relation(fields: [departmentId], references: [id])
  
  // ERP Relations
  createdProducts Product[] @relation("CreatedBy")
  updatedProducts Product[] @relation("UpdatedBy")
  createdOrders   Order[]   @relation("CreatedBy")
  assignedOrders  Order[]   @relation("AssignedTo")
}

model Department {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  managerId   String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  users User[]
}

// Product Management
model Category {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?
  parentId    String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  parent      Category? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryHierarchy")
  products    Product[]
}

model Product {
  id          String      @id @default(cuid())
  name        String
  description String?
  sku         String      @unique
  barcode     String?     @unique
  categoryId  String
  unitPrice   Decimal     @db.Decimal(10, 2)
  costPrice   Decimal     @db.Decimal(10, 2)
  status      ProductStatus @default(ACTIVE)
  createdById String
  updatedById String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  category    Category    @relation(fields: [categoryId], references: [id])
  createdBy   User        @relation("CreatedBy", fields: [createdById], references: [id])
  updatedBy   User?       @relation("UpdatedBy", fields: [updatedById], references: [id])
  inventory   Inventory[]
  orderItems  OrderItem[]
}

model Inventory {
  id          String   @id @default(cuid())
  productId   String
  warehouseId String
  quantity    Int      @default(0)
  minStock    Int      @default(0)
  maxStock    Int?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  product   Product   @relation(fields: [productId], references: [id])
  warehouse Warehouse @relation(fields: [warehouseId], references: [id])

  @@unique([productId, warehouseId])
}

model Warehouse {
  id        String   @id @default(cuid())
  name      String   @unique
  address   String?
  city      String?
  state     String?
  zipCode   String?
  country   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  inventory Inventory[]
}

// Customer Management
model Customer {
  id          String        @id @default(cuid())
  name        String
  email       String?       @unique
  phone       String?
  address     String?
  city        String?
  state       String?
  zipCode     String?
  country     String?
  status      CustomerStatus @default(ACTIVE)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  orders Order[]
}

// Order Management
model Order {
  id          String      @id @default(cuid())
  orderNumber String      @unique
  customerId  String
  status      OrderStatus @default(PENDING)
  totalAmount Decimal     @db.Decimal(10, 2)
  taxAmount   Decimal     @db.Decimal(10, 2) @default(0)
  discountAmount Decimal  @db.Decimal(10, 2) @default(0)
  notes       String?
  createdById String
  assignedToId String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  customer    Customer    @relation(fields: [customerId], references: [id])
  createdBy   User        @relation("CreatedBy", fields: [createdById], references: [id])
  assignedTo  User?       @relation("AssignedTo", fields: [assignedToId], references: [id])
  orderItems  OrderItem[]
}

model OrderItem {
  id        String  @id @default(cuid())
  orderId   String
  productId String
  quantity  Int
  unitPrice Decimal @db.Decimal(10, 2)
  totalPrice Decimal @db.Decimal(10, 2)

  // Relations
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@unique([orderId, productId])
}

// Enums
enum Role {
  ADMIN
  MANAGER
  USER
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum ProductStatus {
  ACTIVE
  INACTIVE
  DISCONTINUED
}

enum CustomerStatus {
  ACTIVE
  INACTIVE
  BLOCKED
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}
