import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create departments
  const itDepartment = await prisma.department.upsert({
    where: { name: 'Information Technology' },
    update: {},
    create: {
      name: 'Information Technology',
      description: 'IT Department responsible for technology infrastructure',
    },
  })

  const salesDepartment = await prisma.department.upsert({
    where: { name: 'Sales' },
    update: {},
    create: {
      name: 'Sales',
      description: 'Sales Department responsible for revenue generation',
    },
  })

  const inventoryDepartment = await prisma.department.upsert({
    where: { name: 'Inventory' },
    update: {},
    create: {
      name: 'Inventory',
      description: 'Inventory Department responsible for stock management',
    },
  })

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 12)
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'System Administrator',
      password: hashedPassword,
      role: 'ADMIN',
      status: 'ACTIVE',
      departmentId: itDepartment.id,
    },
  })

  // Create manager user
  const managerPassword = await bcrypt.hash('manager123', 12)
  
  const managerUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Sales Manager',
      password: managerPassword,
      role: 'MANAGER',
      status: 'ACTIVE',
      departmentId: salesDepartment.id,
    },
  })

  // Create regular user
  const userPassword = await bcrypt.hash('user123', 12)
  
  const regularUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Inventory Clerk',
      password: userPassword,
      role: 'USER',
      status: 'ACTIVE',
      departmentId: inventoryDepartment.id,
    },
  })

  // Create categories
  const electronicsCategory = await prisma.category.upsert({
    where: { name: 'Electronics' },
    update: {},
    create: {
      name: 'Electronics',
      description: 'Electronic devices and components',
    },
  })

  const furnitureCategory = await prisma.category.upsert({
    where: { name: 'Furniture' },
    update: {},
    create: {
      name: 'Furniture',
      description: 'Office and home furniture',
    },
  })

  // Create warehouse
  const mainWarehouse = await prisma.warehouse.upsert({
    where: { name: 'Main Warehouse' },
    update: {},
    create: {
      name: 'Main Warehouse',
      address: '123 Industrial Blvd',
      city: 'Business City',
      state: 'BC',
      zipCode: '12345',
      country: 'USA',
    },
  })

  // Create sample products
  const laptop = await prisma.product.upsert({
    where: { sku: 'EL-LAP-001' },
    update: {},
    create: {
      name: 'Business Laptop',
      description: 'High-performance laptop for business use',
      sku: 'EL-LAP-001',
      barcode: '1234567890123',
      categoryId: electronicsCategory.id,
      unitPrice: 999.99,
      costPrice: 750.00,
      status: 'ACTIVE',
      createdById: adminUser.id,
    },
  })

  const desk = await prisma.product.upsert({
    where: { sku: 'FU-DES-001' },
    update: {},
    create: {
      name: 'Office Desk',
      description: 'Ergonomic office desk with storage',
      sku: 'FU-DES-001',
      barcode: '1234567890124',
      categoryId: furnitureCategory.id,
      unitPrice: 299.99,
      costPrice: 200.00,
      status: 'ACTIVE',
      createdById: adminUser.id,
    },
  })

  // Create inventory records
  await prisma.inventory.upsert({
    where: {
      productId_warehouseId: {
        productId: laptop.id,
        warehouseId: mainWarehouse.id,
      },
    },
    update: {},
    create: {
      productId: laptop.id,
      warehouseId: mainWarehouse.id,
      quantity: 50,
      minStock: 10,
      maxStock: 100,
    },
  })

  await prisma.inventory.upsert({
    where: {
      productId_warehouseId: {
        productId: desk.id,
        warehouseId: mainWarehouse.id,
      },
    },
    update: {},
    create: {
      productId: desk.id,
      warehouseId: mainWarehouse.id,
      quantity: 25,
      minStock: 5,
      maxStock: 50,
    },
  })

  // Create sample customer
  const customer = await prisma.customer.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'ABC Corporation',
      email: '<EMAIL>',
      phone: '******-0123',
      address: '456 Business Ave',
      city: 'Commerce City',
      state: 'CC',
      zipCode: '54321',
      country: 'USA',
      status: 'ACTIVE',
    },
  })

  console.log('✅ Database seeded successfully!')
  console.log('👤 Admin user: <EMAIL> / admin123')
  console.log('👤 Manager user: <EMAIL> / manager123')
  console.log('👤 Regular user: <EMAIL> / user123')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
