"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { formatDateTime } from "@/lib/utils"

interface Activity {
  id: string
  type: 'order' | 'product' | 'user' | 'inventory'
  title: string
  description: string
  timestamp: Date
  user: {
    name: string
    avatar?: string
  }
}

const mockActivities: Activity[] = [
  {
    id: '1',
    type: 'order',
    title: 'New Order Created',
    description: 'Order #ORD-123456 created by ABC Corporation',
    timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
    user: {
      name: '<PERSON>',
      avatar: undefined,
    },
  },
  {
    id: '2',
    type: 'product',
    title: 'Product Updated',
    description: 'Business Laptop price updated to $999.99',
    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    user: {
      name: '<PERSON>',
      avatar: undefined,
    },
  },
  {
    id: '3',
    type: 'inventory',
    title: 'Low Stock Alert',
    description: 'Office Desk stock is running low (5 remaining)',
    timestamp: new Date(Date.now() - 1000 * 60 * 45), // 45 minutes ago
    user: {
      name: 'System',
      avatar: undefined,
    },
  },
  {
    id: '4',
    type: 'user',
    title: 'New User Registered',
    description: '<EMAIL> joined the system',
    timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
    user: {
      name: 'Admin',
      avatar: undefined,
    },
  },
]

function getActivityIcon(type: Activity['type']) {
  switch (type) {
    case 'order':
      return '🛒'
    case 'product':
      return '📦'
    case 'user':
      return '👤'
    case 'inventory':
      return '⚠️'
    default:
      return '📋'
  }
}

function getActivityColor(type: Activity['type']) {
  switch (type) {
    case 'order':
      return 'text-blue-600'
    case 'product':
      return 'text-green-600'
    case 'user':
      return 'text-purple-600'
    case 'inventory':
      return 'text-orange-600'
    default:
      return 'text-gray-600'
  }
}

export function RecentActivity() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
        <CardDescription>
          Latest updates from your ERP system
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {mockActivities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center text-sm">
                  {getActivityIcon(activity.type)}
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className={`text-sm font-medium ${getActivityColor(activity.type)}`}>
                    {activity.title}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {formatDateTime(activity.timestamp)}
                  </p>
                </div>
                <p className="text-sm text-muted-foreground">
                  {activity.description}
                </p>
                <div className="flex items-center mt-1">
                  <Avatar className="h-4 w-4">
                    <AvatarImage src={activity.user.avatar} />
                    <AvatarFallback className="text-xs">
                      {activity.user.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-xs text-muted-foreground ml-1">
                    {activity.user.name}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
