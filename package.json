{"name": "free-nextjs-admin-dashboard", "version": "2.0.2", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@react-jvectormap/core": "^1.0.4", "@react-jvectormap/world": "^1.1.2", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/postcss": "^4.0.9", "apexcharts": "^4.3.0", "autoprefixer": "^10.4.20", "flatpickr": "^4.6.13", "next": "15.2.3", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "swiper": "^11.2.0", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-transition-group": "^4.4.12", "eslint": "^9", "eslint-config-next": "15.1.3", "postcss": "^8", "tailwindcss": "^4.0.0", "typescript": "^5"}, "overrides": {"@react-jvectormap/core": {"react": "^16.8.0 || ^17 || ^18 || ^19", "react-dom": "^16.8.0 || ^17 || ^18 || ^19"}, "@react-jvectormap/world": {"react": "^16.8.0 || ^17 || ^18 || ^19", "react-dom": "^16.8.0 || ^17 || ^18 || ^19"}}}