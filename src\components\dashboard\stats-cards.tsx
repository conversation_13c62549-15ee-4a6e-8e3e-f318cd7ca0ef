"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { useQuery } from "@tanstack/react-query"
import { 
  Users, 
  Package, 
  ShoppingCart, 
  DollarSign,
  TrendingUp,
  TrendingDown
} from "lucide-react"

interface StatsData {
  totalUsers: number
  totalProducts: number
  totalOrders: number
  totalRevenue: number
  userGrowth: number
  productGrowth: number
  orderGrowth: number
  revenueGrowth: number
}

async function fetchStats(): Promise<StatsData> {
  // This would be replaced with actual API calls
  return {
    totalUsers: 1234,
    totalProducts: 567,
    totalOrders: 890,
    totalRevenue: 123456.78,
    userGrowth: 12.5,
    productGrowth: 8.2,
    orderGrowth: -2.1,
    revenueGrowth: 15.3,
  }
}

export function StatsCards() {
  const { data: stats, isLoading } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: fetchStats,
  })

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Loading...</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">--</div>
              <p className="text-xs text-muted-foreground">--</p>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (!stats) return null

  const cards = [
    {
      title: "Total Users",
      value: stats.totalUsers.toLocaleString(),
      change: stats.userGrowth,
      icon: Users,
    },
    {
      title: "Total Products",
      value: stats.totalProducts.toLocaleString(),
      change: stats.productGrowth,
      icon: Package,
    },
    {
      title: "Total Orders",
      value: stats.totalOrders.toLocaleString(),
      change: stats.orderGrowth,
      icon: ShoppingCart,
    },
    {
      title: "Total Revenue",
      value: `$${stats.totalRevenue.toLocaleString()}`,
      change: stats.revenueGrowth,
      icon: DollarSign,
    },
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {cards.map((card) => {
        const Icon = card.icon
        const isPositive = card.change > 0
        const TrendIcon = isPositive ? TrendingUp : TrendingDown
        
        return (
          <Card key={card.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {card.title}
              </CardTitle>
              <Icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              <p className="text-xs text-muted-foreground flex items-center">
                <TrendIcon 
                  className={`h-3 w-3 mr-1 ${
                    isPositive ? 'text-green-500' : 'text-red-500'
                  }`} 
                />
                <span className={isPositive ? 'text-green-500' : 'text-red-500'}>
                  {Math.abs(card.change)}%
                </span>
                <span className="ml-1">from last month</span>
              </p>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
