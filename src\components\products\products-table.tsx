"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { formatCurrency, formatDate } from "@/lib/utils"
import { Search, Filter, MoreHorizontal, Edit, Trash2 } from "lucide-react"

interface Product {
  id: string
  name: string
  description?: string
  sku: string
  barcode?: string
  categoryId: string
  unitPrice: number
  costPrice: number
  status: 'ACTIVE' | 'INACTIVE' | 'DISCONTINUED'
  createdAt: string
  updatedAt: string
  category?: {
    id: string
    name: string
  }
  inventory?: Array<{
    quantity: number
    warehouse: {
      name: string
    }
  }>
}

async function fetchProducts(page: number, search: string): Promise<{
  products: Product[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}> {
  // Mock data for now - replace with actual API call
  const mockProducts: Product[] = [
    {
      id: '1',
      name: 'Business Laptop',
      description: 'High-performance laptop for business use',
      sku: 'EL-LAP-001',
      barcode: '1234567890123',
      categoryId: '1',
      unitPrice: 999.99,
      costPrice: 750.00,
      status: 'ACTIVE',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      category: {
        id: '1',
        name: 'Electronics'
      },
      inventory: [
        {
          quantity: 50,
          warehouse: {
            name: 'Main Warehouse'
          }
        }
      ]
    },
    {
      id: '2',
      name: 'Office Desk',
      description: 'Ergonomic office desk with storage',
      sku: 'FU-DES-001',
      barcode: '1234567890124',
      categoryId: '2',
      unitPrice: 299.99,
      costPrice: 200.00,
      status: 'ACTIVE',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      category: {
        id: '2',
        name: 'Furniture'
      },
      inventory: [
        {
          quantity: 25,
          warehouse: {
            name: 'Main Warehouse'
          }
        }
      ]
    }
  ]

  // Filter products based on search
  const filteredProducts = search 
    ? mockProducts.filter(product => 
        product.name.toLowerCase().includes(search.toLowerCase()) ||
        product.sku.toLowerCase().includes(search.toLowerCase())
      )
    : mockProducts

  return {
    products: filteredProducts,
    pagination: {
      page,
      limit: 10,
      total: filteredProducts.length,
      pages: Math.ceil(filteredProducts.length / 10)
    }
  }
}

function getStatusBadge(status: Product['status']) {
  switch (status) {
    case 'ACTIVE':
      return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>
    case 'INACTIVE':
      return <Badge variant="secondary">Inactive</Badge>
    case 'DISCONTINUED':
      return <Badge variant="destructive">Discontinued</Badge>
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

export function ProductsTable() {
  const [page, setPage] = useState(1)
  const [search, setSearch] = useState("")

  const { data, isLoading, error } = useQuery({
    queryKey: ['products', page, search],
    queryFn: () => fetchProducts(page, search),
  })

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            Error loading products. Please try again.
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Product Inventory</CardTitle>
        <CardDescription>
          A list of all products in your inventory
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-2 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search products..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-8"
            />
          </div>
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
        </div>

        {isLoading ? (
          <div className="text-center py-8">Loading products...</div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Stock</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="w-[70px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data?.products.map((product) => (
                  <TableRow key={product.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{product.name}</div>
                        {product.description && (
                          <div className="text-sm text-muted-foreground">
                            {product.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="font-mono text-sm">
                      {product.sku}
                    </TableCell>
                    <TableCell>{product.category?.name}</TableCell>
                    <TableCell>{formatCurrency(product.unitPrice)}</TableCell>
                    <TableCell>
                      {product.inventory?.[0]?.quantity || 0}
                    </TableCell>
                    <TableCell>{getStatusBadge(product.status)}</TableCell>
                    <TableCell>{formatDate(product.createdAt)}</TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        {data && data.pagination.pages > 1 && (
          <div className="flex items-center justify-between space-x-2 py-4">
            <div className="text-sm text-muted-foreground">
              Showing {((page - 1) * data.pagination.limit) + 1} to{' '}
              {Math.min(page * data.pagination.limit, data.pagination.total)} of{' '}
              {data.pagination.total} products
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page - 1)}
                disabled={page <= 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page + 1)}
                disabled={page >= data.pagination.pages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
